package ai.beyz.worker.config;

import com.google.common.util.concurrent.RateLimiter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RateLimiterConfig {

    @Bean
    public RateLimiter rateLimiter() {
        // 目标：每分钟 30 个消息
        // 计算：30.0 / 60.0 = 0.5
        // 这意味着每秒钟生成 0.5 个许可
        double permitsPerSecond = 0.8;
        return RateLimiter.create(permitsPerSecond);
    }
}
